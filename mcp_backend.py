"""
FastAPI Backend for MCP Client with Bedrock Integration - ENHANCED VERSION
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import json
import logging
from contextlib import asynccontextmanager, AsyncExitStack
import uvicorn
import boto3
from botocore.config import Config
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import os
from dotenv import load_dotenv
import uuid
import anyio

load_dotenv()

try:
    from jsonschema import validate, Draft202012Validator, ValidationError
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

# Simple telemetry tracking
class TelemetryTracker:
    """Simple telemetry tracker for performance monitoring"""
    def __init__(self):
        self.metrics = {
            "total_requests": 0,
            "total_tool_calls": 0,
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_latency_ms": 0,
            "error_count": 0,
            "tool_execution_times": [],
            "conversation_turns": 0
        }

    def record_conversation_turn(self, usage: Dict, metrics: Dict, tool_count: int = 0):
        """Record metrics from a conversation turn"""
        self.metrics["total_requests"] += 1
        self.metrics["conversation_turns"] += 1
        self.metrics["total_tool_calls"] += tool_count
        self.metrics["total_input_tokens"] += usage.get("inputTokens", 0)
        self.metrics["total_output_tokens"] += usage.get("outputTokens", 0)
        self.metrics["total_latency_ms"] += metrics.get("latencyMs", 0)

    def record_error(self):
        """Record an error occurrence"""
        self.metrics["error_count"] += 1

    def record_tool_execution_time(self, execution_time_ms: float):
        """Record tool execution time"""
        self.metrics["tool_execution_times"].append(execution_time_ms)

    def get_summary(self) -> Dict:
        """Get telemetry summary"""
        avg_latency = (self.metrics["total_latency_ms"] / max(1, self.metrics["total_requests"]))
        avg_tool_time = (sum(self.metrics["tool_execution_times"]) / 
                        max(1, len(self.metrics["tool_execution_times"])))
        
        return {
            **self.metrics,
            "average_latency_ms": avg_latency,
            "average_tool_execution_ms": avg_tool_time,
            "success_rate": 1 - (self.metrics["error_count"] / max(1, self.metrics["total_requests"]))
        }

# Global telemetry tracker
telemetry = TelemetryTracker()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic Models
class MCPServerConfig(BaseModel):
    name: str
    command: str
    args: List[str] = []
    env: Dict[str, str] = {}
    description: str = ""
    enabled: bool = True

class ChatMessage(BaseModel):
    role: str
    content: str
    timestamp: Optional[str] = None

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    use_tools: bool = True

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"

class MCPServerConnection:
    """Represents a connection to an MCP server."""

    def __init__(self, config: MCPServerConfig):
        self.config = config
        self.status = "disconnected"
        self.tools = {}
        self.resources = {}
        self.error = None
        self.session = None
        self.exit_stack = None

    async def connect(self) -> bool:
        """Connect to the MCP server."""
        try:
            self.status = "connecting"
            self.error = None

            # Create server parameters
            server_params = StdioServerParameters(
                command=self.config.command,
                args=self.config.args,
                env=self.config.env
            )

            # Create exit stack for cleanup
            self.exit_stack = AsyncExitStack()

            # Connect to server
            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )

            # Create session
            self.session = await self.exit_stack.enter_async_context(
                ClientSession(stdio_transport[0], stdio_transport[1])
            )

            # Initialize session
            await self.session.initialize()

            # Get tools and resources
            tools_result = await self.session.list_tools()
            self.tools = {tool.name: tool for tool in tools_result.tools}

            try:
                resources_result = await self.session.list_resources()
                self.resources = {resource.name: resource for resource in resources_result.resources}
            except Exception as e:
                logger.warning(f"Could not list resources for {self.config.name}: {e}")
                self.resources = {}

            self.status = "connected"
            logger.info(f"Connected to {self.config.name} with {len(self.tools)} tools and {len(self.resources)} resources")
            return True

        except Exception as e:
            self.status = "error"
            self.error = str(e)
            logger.error(f"Failed to connect to {self.config.name}: {e}")
            await self.cleanup()
            return False

    async def cleanup(self):
        """Clean up the connection."""
        if self.exit_stack:
            try:
                await self.exit_stack.aclose()
            except Exception as e:
                logger.warning(f"Error during cleanup for {self.config.name}: {e}")
            finally:
                self.exit_stack = None
                self.session = None
                self.status = "disconnected"

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on this server."""
        if self.status != "connected" or not self.session:
            raise RuntimeError(f"Server {self.config.name} is not connected")

        if tool_name not in self.tools:
            raise ValueError(f"Tool {tool_name} not found on server {self.config.name}")

        try:
            result = await self.session.call_tool(tool_name, arguments)
            return {
                "success": True,
                "result": result.content,
                "server": self.config.name,
                "tool": tool_name
            }
        except Exception as e:
            logger.error(f"Error calling tool {tool_name} on {self.config.name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "server": self.config.name,
                "tool": tool_name
            }


class MCPClientManager:
    """Manages multiple MCP server connections."""

    def __init__(self):
        self.connections: Dict[str, MCPServerConnection] = {}
        logger.info("MCP Client Manager initialized")

    async def add_server(self, config: MCPServerConfig) -> bool:
        """Add and connect to an MCP server."""
        if config.name in self.connections:
            await self.remove_server(config.name)

        connection = MCPServerConnection(config)
        self.connections[config.name] = connection

        success = await connection.connect()
        if not success:
            logger.error(f"Failed to connect to server {config.name}")

        return success

    async def remove_server(self, server_name: str):
        """Remove and disconnect from an MCP server."""
        if server_name in self.connections:
            await self.connections[server_name].cleanup()
            del self.connections[server_name]
            logger.info(f"Removed server {server_name}")

    async def cleanup(self):
        """Clean up all connections."""
        for connection in self.connections.values():
            await connection.cleanup()
        self.connections.clear()
        logger.info("All MCP connections cleaned up")

    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get all available tools from all connected servers."""
        tools = {}
        for server_name, connection in self.connections.items():
            if connection.status == "connected":
                for tool_name, tool in connection.tools.items():
                    key = f"{server_name}::{tool_name}"
                    tools[key] = {
                        "server": server_name,
                        "tool": {
                            "name": tool_name,
                            "description": getattr(tool, 'description', ''),
                            "input_schema": getattr(tool, 'inputSchema', {})
                        }
                    }
        return tools

    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on a specific server."""
        if server_name not in self.connections:
            raise ValueError(f"Server {server_name} not found")

        connection = self.connections[server_name]
        return await connection.call_tool(tool_name, arguments)

# Helper function to get correct executable name based on OS
def get_executable_name(base_name: str) -> str:
    """Get the correct executable name based on the operating system"""
    if os.name == 'nt':  # Windows
        return f"{base_name}.exe"
    else:  # Unix-like systems
        return base_name.replace('.', '-')

# ENHANCED Default server configurations with additional servers
DEFAULT_MCP_SERVERS = [
    MCPServerConfig(
        name="cost-explorer",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cost-explorer-mcp-server@latest",
            get_executable_name("awslabs.cost-explorer-mcp-server")
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Cost Explorer MCP Server for cost analysis and billing insights",
        enabled=True
    ),
    
    MCPServerConfig(
        name="cloudformation",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cfn-mcp-server@latest",
            get_executable_name("awslabs.cfn-mcp-server")
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        description="AWS CloudFormation MCP Server for infrastructure management",
        enabled=True
    ),
    
    MCPServerConfig(
        name="aws-pricing",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-aws-pricing-mcp-server@latest",
            get_executable_name("awslabs.aws-pricing-mcp-server")
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Pricing MCP Server for real-time service pricing information",
        enabled=True
    ),

    # NEW ENHANCED SERVERS
    MCPServerConfig(
        name="billing-cost-management",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-billing-cost-management-mcp-server@latest",
            get_executable_name("awslabs.billing-cost-management-mcp-server")
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        description="AWS Billing and Cost Management for optimization recommendations",
        enabled=True
    ),

    MCPServerConfig(
        name="aws-diagram",
        command="uv", 
        args=[
            "tool", "run", "--from",
            "awslabs-aws-diagram-mcp-server@latest",
            get_executable_name("awslabs.aws-diagram-mcp-server")
        ],
        env={"AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")},
        description="AWS architecture diagram generation",
        enabled=True
    ),

    MCPServerConfig(
        name="aws-cdk",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-aws-cdk-mcp-server@latest",
            get_executable_name("awslabs.aws-cdk-mcp-server")
        ], 
        env={"AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")},
        description="AWS CDK best practices and Well-Architected templates",
        enabled=True
    ),

    MCPServerConfig(
        name="aws-documentation", 
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-aws-documentation-mcp-server@latest",
            get_executable_name("awslabs.aws-documentation-mcp-server")
        ],
        description="Latest AWS documentation and best practices",
        enabled=True
    ),

    MCPServerConfig(
        name="cloud-control-api",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-ccapi-mcp-server@latest",
            get_executable_name("awslabs.ccapi-mcp-server")
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        description="Comprehensive AWS resource management with 1100+ resource types",
        enabled=True
    ),

    MCPServerConfig(
        name="aws-cloudwatch",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cloudwatch-mcp-server@latest",
            get_executable_name("awslabs.cloudwatch-mcp-server")
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        description="CloudWatch monitoring, metrics, and alerting design",
        enabled=True
    )
]

# [Rest of the existing main.py code remains the same - lifespan, app creation, endpoints, etc.]
# ... [Previous code for lifespan, FastAPI app, endpoints] ...

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info")
    )
