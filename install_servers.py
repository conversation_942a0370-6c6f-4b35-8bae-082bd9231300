#!/usr/bin/env python3
"""
Enhanced MCP Server Installation Script

Installs and configures all 9 AWS MCP servers for comprehensive cost optimization and architecture design
"""

import requests
import json
import os
import sys
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API base URL
API_BASE = "http://localhost:8000"

# Enhanced MCP servers configuration
DEFAULT_SERVERS = [
    {
        "name": "cost-explorer",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-cost-explorer-mcp-server@latest",
            "awslabs.cost-explorer-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        "description": "AWS Cost Explorer MCP Server for cost analysis and billing insights",
        "enabled": True
    },
    {
        "name": "cloudformation",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-cfn-mcp-server@latest",
            "awslabs.cfn-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        "description": "AWS CloudFormation MCP Server for infrastructure management",
        "enabled": True
    },
    {
        "name": "aws-pricing",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-aws-pricing-mcp-server@latest",
            "awslabs.aws-pricing-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        "description": "AWS Pricing MCP Server for real-time service pricing information",
        "enabled": True
    },
    {
        "name": "billing-cost-management",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-billing-cost-management-mcp-server@latest",
            "awslabs.billing-cost-management-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        "description": "AWS Billing and Cost Management MCP Server for optimization and billing insights",
        "enabled": True
    },
    {
        "name": "aws-diagram",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-aws-diagram-mcp-server@latest",
            "awslabs.aws-diagram-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        "description": "AWS Diagram MCP Server for architecture diagram generation",
        "enabled": True
    },
    {
        "name": "aws-cdk",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-aws-cdk-mcp-server@latest",
            "awslabs.aws-cdk-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        "description": "AWS CDK MCP Server for Well-Architected templates and best practices",
        "enabled": True
    },
    {
        "name": "aws-documentation",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-aws-documentation-mcp-server@latest",
            "awslabs.aws-documentation-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        "description": "AWS Documentation MCP Server for real-time documentation access",
        "enabled": True
    },
    {
        "name": "cloud-control-api",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-ccapi-mcp-server@latest",
            "awslabs.ccapi-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "DEFAULT_TAGS": "enabled",
            "SECURITY_SCANNING": "enabled",
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        "description": "AWS Cloud Control API MCP Server for comprehensive resource management",
        "enabled": True
    },
    {
        "name": "aws-cloudwatch",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-cloudwatch-mcp-server@latest",
            "awslabs.cloudwatch-mcp-server"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        "description": "AWS CloudWatch MCP Server for monitoring strategy and alerting",
        "enabled": True
    }
]

def check_api_health():
    """Check if the API is running"""
    try:
        response = requests.get(f"{API_BASE}/", timeout=10)
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        return False
    except Exception:
        return False

def configure_server_with_retry(server_config, max_retries=3):
    """Configure a single server with retry logic"""
    for attempt in range(max_retries):
        try:
            print(f"🔧 Configuring {server_config['name']} (attempt {attempt + 1}/{max_retries})")
            
            response = requests.post(
                f"{API_BASE}/servers",
                json=server_config,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 200:
                print(f"✅ Successfully configured {server_config['name']}")
                return True
            else:
                print(f"❌ Failed to configure {server_config['name']}: {response.text}")
                if attempt < max_retries - 1:
                    print(f"🔄 Retrying in 5 seconds...")
                    time.sleep(5)
                    
        except requests.exceptions.Timeout:
            print(f"⏱️  Timeout configuring {server_config['name']}")
            if attempt < max_retries - 1:
                print(f"🔄 Retrying in 10 seconds...")
                time.sleep(10)
        except Exception as e:
            print(f"❌ Error configuring {server_config['name']}: {e}")
            if attempt < max_retries - 1:
                print(f"🔄 Retrying in 5 seconds...")
                time.sleep(5)
    
    return False

def list_servers_detailed():
    """List all configured servers with detailed status"""
    try:
        response = requests.get(f"{API_BASE}/servers", timeout=30)
        if response.status_code == 200:
            servers = response.json()
            print("\n" + "="*70)
            print("📋 MCP SERVER STATUS REPORT")
            print("="*70)
            
            connected_count = 0
            total_tools = 0
            
            for name, info in servers.items():
                status_emoji = "🟢" if info["status"] == "connected" else "🔴"
                tools_count = info.get('tools_count', 0)
                
                print(f"{status_emoji} {name.upper()}")
                print(f"   Status: {info['status']}")
                print(f"   Tools: {tools_count}")
                print(f"   Description: {info.get('description', 'N/A')}")
                
                if info.get('error'):
                    print(f"   ⚠️  Error: {info['error']}")
                
                if info["status"] == "connected":
                    connected_count += 1
                    total_tools += tools_count
                
                print()
            
            print("="*70)
            print(f"📊 SUMMARY:")
            print(f"   Connected Servers: {connected_count}/{len(servers)}")
            print(f"   Total Available Tools: {total_tools}")
            print(f"   Success Rate: {connected_count/len(servers)*100:.1f}%")
            print("="*70)
            
            return True, connected_count, len(servers)
        else:
            print(f"❌ Failed to list servers: {response.text}")
            return False, 0, 0
    except Exception as e:
        print(f"❌ Error listing servers: {e}")
        return False, 0, 0

def setup_all_servers():
    """Set up all default servers with enhanced logging"""
    print("\n🚀 ENHANCED AWS MCP SERVER INSTALLATION")
    print("="*70)
    print(f"Installing {len(DEFAULT_SERVERS)} specialized AWS MCP servers:")
    
    categories = {
        "Cost Analysis": ["cost-explorer", "aws-pricing", "billing-cost-management"],
        "Architecture Design": ["aws-diagram", "aws-cdk", "cloudformation"],
        "Resource Management": ["cloud-control-api", "aws-cloudwatch"],
        "Documentation": ["aws-documentation"]
    }
    
    for category, servers in categories.items():
        print(f"\n📂 {category}:")
        for server in servers:
            server_info = next((s for s in DEFAULT_SERVERS if s["name"] == server), None)
            if server_info:
                print(f"   • {server_info['description']}")
    
    print("\n" + "="*70)
    
    success_count = 0
    failed_servers = []
    
    for i, server_config in enumerate(DEFAULT_SERVERS, 1):
        print(f"\n[{i}/{len(DEFAULT_SERVERS)}] Installing {server_config['name']}...")
        
        if configure_server_with_retry(server_config):
            success_count += 1
        else:
            failed_servers.append(server_config['name'])
        
        # Add delay between server installations to avoid resource conflicts
        if i < len(DEFAULT_SERVERS):
            time.sleep(2)
    
    print(f"\n" + "="*70)
    print(f"🎉 INSTALLATION COMPLETE!")
    print(f"   ✅ Successful: {success_count}/{len(DEFAULT_SERVERS)} servers")
    
    if failed_servers:
        print(f"   ❌ Failed: {', '.join(failed_servers)}")
        print(f"\n💡 To retry failed servers, run:")
        print(f"   python {sys.argv[0]} retry")
    
    return success_count == len(DEFAULT_SERVERS)

def main():
    """Main function with enhanced command handling"""
    print("🧠 Enhanced AWS Cost Optimization & Architecture Design")
    print("   MCP Server Installation Tool")
    print("="*70)
    
    if len(sys.argv) < 2:
        print("Usage: python install_mcp_servers.py <command>")
        print("\nCommands:")
        print("  install  - Install all MCP servers")
        print("  status   - Check status of all servers")
        print("  health   - Check API health")
        print("  retry    - Retry failed server installations")
        sys.exit(1)

    command = sys.argv[1].lower()

    # Check API health first
    print("🔍 Checking API health...")
    if not check_api_health():
        print("❌ API is not running. Please start the FastAPI server first:")
        print("   python main_enhanced.py")
        print("\nOr for basic version:")
        print("   python main.py")
        sys.exit(1)
    
    print("✅ API is running\n")

    if command == "install":
        setup_all_servers()
        print("\n📊 Checking final server status...")
        list_servers_detailed()
        
    elif command == "status":
        success, connected, total = list_servers_detailed()
        if connected < total:
            print(f"\n💡 To retry failed servers, run:")
            print(f"   python {sys.argv[0]} install")
            
    elif command == "health":
        print("✅ API health check passed")
        
    elif command == "retry":
        print("🔄 Retrying server installation...")
        setup_all_servers()
        
    else:
        print(f"❌ Unknown command: {command}")
        print("Use 'install', 'status', 'health', or 'retry'")
        sys.exit(1)

if __name__ == "__main__":
    main()
