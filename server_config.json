{"mcpServers": {"cost-explorer": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-cost-explorer-mcp-server@latest", "awslabs.cost-explorer-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1", "FASTMCP_LOG_LEVEL": "ERROR"}}, "cloudformation": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-cfn-mcp-server@latest", "awslabs.cfn-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"}}, "aws-pricing": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-aws-pricing-mcp-server@latest", "awslabs.aws-pricing-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1", "FASTMCP_LOG_LEVEL": "ERROR"}}, "billing-cost-management": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-billing-cost-management-mcp-server@latest", "awslabs.billing-cost-management-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1", "FASTMCP_LOG_LEVEL": "ERROR"}}, "aws-diagram": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-aws-diagram-mcp-server@latest", "awslabs.aws-diagram-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"}}, "aws-cdk": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-aws-cdk-mcp-server@latest", "awslabs.aws-cdk-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"}}, "aws-documentation": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-aws-documentation-mcp-server@latest", "awslabs.aws-documentation-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"}}, "cloud-control-api": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-ccapi-mcp-server@latest", "awslabs.ccapi-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1", "DEFAULT_TAGS": "enabled", "SECURITY_SCANNING": "enabled", "FASTMCP_LOG_LEVEL": "ERROR"}}, "aws-cloudwatch": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-cloudwatch-mcp-server@latest", "awslabs.cloudwatch-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"}}}}