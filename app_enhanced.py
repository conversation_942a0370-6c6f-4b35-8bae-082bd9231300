"""
Enhanced Streamlit Frontend with Specialized AWS Cost and Architecture Capabilities
"""

import streamlit as st
import requests
import json
import uuid
from datetime import datetime
import time
import pandas as pd
from typing import Optional, Dict, List, Any
import plotly.express as px
import plotly.graph_objects as go
from urllib.parse import urljoin
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))

# Page configuration
st.set_page_config(
    page_title="AWS Cost Optimization & Architecture Design Assistant",
    page_icon="🏗️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS
st.markdown("""
<style>
    .specialized-section {
        background-color: #f0f8ff;
        padding: 20px;
        border-radius: 10px;
        border-left: 5px solid #1f77b4;
        margin: 10px 0;
    }
    
    .cost-analysis {
        background-color: #f0fff0;
        border-left-color: #32cd32;
    }
    
    .architecture-design {
        background-color: #fff8f0;
        border-left-color: #ff8c00;
    }
    
    .success-metric {
        color: #32cd32;
        font-weight: bold;
    }
    
    .warning-metric {
        color: #ff8c00;
        font-weight: bold;
    }
    
    .error-metric {
        color: #ff0000;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

class EnhancedAPIClient:
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling"""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        try:
            response = requests.request(method, url, timeout=self.timeout, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.Timeout:
            st.error(f"Request timed out after {self.timeout} seconds")
            raise
        except requests.exceptions.ConnectionError:
            st.error(f"Unable to connect to the API server at {self.base_url}")
            raise
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                st.error("Service is initializing. Please wait a moment and try again.")
            else:
                st.error(f"API Error ({e.response.status_code}): {e.response.text}")
            raise
        except Exception as e:
            st.error(f"Unexpected error: {str(e)}")
            raise

    def chat_with_context(self, message: str, session_id: str) -> Dict:
        """Send chat message with context retention"""
        payload = {
            "message": message,
            "conversation_id": session_id,
            "use_tools": True
        }
        response = self._make_request("POST", "/chat", json=payload)
        return response.json()

    def analyze_service_addition(self, request_data: Dict) -> Dict:
        """Specialized service addition cost analysis"""
        response = self._make_request("POST", "/analyze/service-addition", json=request_data)
        return response.json()

    def design_complete_solution(self, request_data: Dict) -> Dict:
        """Specialized complete solution design"""
        response = self._make_request("POST", "/design/complete-solution", json=request_data)
        return response.json()

    def get_servers_status(self) -> Dict:
        """Get servers status with categorization"""
        response = self._make_request("GET", "/servers")
        return response.json()

    def get_tools_by_category(self) -> Dict:
        """Get tools categorized by capability"""
        response = self._make_request("GET", "/tools")
        return response.json()

# Initialize API client
@st.cache_resource
def get_api_client():
    return EnhancedAPIClient(API_BASE_URL, API_TIMEOUT)

api_client = get_api_client()

# Initialize session state
def initialize_session_state():
    if 'session_id' not in st.session_state:
        st.session_state.session_id = f"streamlit_{uuid.uuid4().hex[:12]}"
    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []

initialize_session_state()

# Header
st.title("🏗️ AWS Cost Optimization & Architecture Design Assistant")
st.markdown("**AI-powered service addition cost analysis and complete solution architecture design**")

# Sidebar - Server Status
with st.sidebar:
    st.header("🔧 System Status")
    
    try:
        servers_status = api_client.get_servers_status()
        
        # Categorize servers
        cost_servers = {k: v for k, v in servers_status.items() if v.get('category') == 'cost_analysis'}
        arch_servers = {k: v for k, v in servers_status.items() if v.get('category') == 'architecture_design'}
        resource_servers = {k: v for k, v in servers_status.items() if v.get('category') == 'resource_management'}
        
        st.subheader("💰 Cost Analysis Servers")
        for name, info in cost_servers.items():
            status_emoji = "🟢" if info["status"] == "connected" else "🔴"
            st.write(f"{status_emoji} **{name}**: {info['tools_count']} tools")
        
        st.subheader("🏗️ Architecture Design Servers")
        for name, info in arch_servers.items():
            status_emoji = "🟢" if info["status"] == "connected" else "🔴"
            st.write(f"{status_emoji} **{name}**: {info['tools_count']} tools")
        
        st.subheader("⚙️ Resource Management Servers")
        for name, info in resource_servers.items():
            status_emoji = "🟢" if info["status"] == "connected" else "🔴"
            st.write(f"{status_emoji} **{name}**: {info['tools_count']} tools")
            
        total_connected = sum(1 for info in servers_status.values() if info["status"] == "connected")
        total_servers = len(servers_status)
        
        st.metric("Connected Servers", f"{total_connected}/{total_servers}")
        
    except Exception as e:
        st.error(f"Failed to get server status: {e}")

# Main interface tabs
tab1, tab2, tab3, tab4 = st.tabs([
    "💰 Service Addition Cost Analysis", 
    "🏗️ Complete Solution Design", 
    "💬 General Chat", 
    "📊 Analytics"
])

# Tab 1: Service Addition Cost Analysis
with tab1:
    st.markdown("""
    <div class="specialized-section cost-analysis">
        <h3>💰 Service Addition Cost Analysis</h3>
        <p>Analyze the incremental cost impact of adding new AWS services to your existing infrastructure.</p>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Current Infrastructure")
        current_services = st.multiselect(
            "Current Services in Use",
            ["EC2", "RDS", "S3", "Lambda", "DynamoDB", "CloudFront", "ELB", "VPC", "Route53", "CloudWatch"],
            default=["EC2", "RDS", "S3"]
        )
        
        current_region = st.selectbox(
            "Current Deployment Region", 
            ["us-east-1", "us-west-2", "ap-south-1", "eu-west-1", "ap-southeast-1"]
        )
        
        monthly_spend = st.number_input(
            "Approximate Current Monthly Spend ($)", 
            min_value=0, 
            max_value=100000, 
            value=1000,
            step=100
        )
    
    with col2:
        st.subheader("Service Addition Details")
        new_service = st.selectbox(
            "Service to Add",
            [
                "ElastiCache (Redis/Memcached)",
                "Amazon CloudFront",
                "Amazon SQS",
                "Amazon SNS", 
                "Amazon ECS/Fargate",
                "Amazon EKS",
                "Amazon OpenSearch",
                "Amazon DocumentDB",
                "AWS WAF",
                "AWS Shield Advanced",
                "Amazon API Gateway"
            ]
        )
        
        usage_pattern = st.selectbox(
            "Expected Usage Pattern", 
            [
                "Light (< 100 req/sec, < 1TB data)",
                "Medium (100-1000 req/sec, 1-10TB data)", 
                "Heavy (> 1000 req/sec, > 10TB data)",
                "Variable (seasonal traffic spikes)"
            ]
        )
        
        timeline = st.selectbox(
            "Implementation Timeline",
            ["Immediate (this month)", "Next quarter", "Next 6 months", "Next year"]
        )
    
    if st.button("🔍 Analyze Service Addition Cost Impact", type="primary", key="analyze_cost"):
        with st.spinner("Analyzing cost impact with real AWS pricing data..."):
            try:
                request_data = {
                    "current_services": current_services,
                    "new_service": new_service,
                    "usage_pattern": usage_pattern,
                    "region": current_region,
                    "current_monthly_spend": monthly_spend,
                    "timeline": timeline
                }
                
                result = api_client.analyze_service_addition(request_data)
                
                st.success("✅ Cost Analysis Complete!")
                
                # Display results
                with st.expander("📊 Detailed Cost Analysis Results", expanded=True):
                    st.markdown(result["response"])
                
                # Show tools used
                if result.get("tools_used"):
                    st.info(f"Analysis used {len(result['tools_used'])} AWS tools for accurate pricing")
                    
            except Exception as e:
                st.error(f"Analysis failed: {e}")

# Tab 2: Complete Solution Design
with tab2:
    st.markdown("""
    <div class="specialized-section architecture-design">
        <h3>🏗️ Complete Solution Architecture Design</h3>
        <p>Design comprehensive AWS solutions from requirements to implementation with cost analysis.</p>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.subheader("Application Requirements")
        app_type = st.selectbox(
            "Application Type",
            [
                "Web Application (Frontend + Backend)",
                "Mobile Backend API",
                "Data Processing Pipeline", 
                "ML/AI Platform",
                "E-commerce Platform",
                "Content Management System",
                "Real-time Analytics Platform",
                "IoT Data Collection Platform"
            ]
        )
        
        expected_users = st.number_input(
            "Expected Concurrent Users", 
            min_value=100, 
            max_value=10000000, 
            value=10000,
            step=1000
        )
        
        data_volume = st.selectbox(
            "Data Volume Estimate",
            ["< 1GB", "1-100GB", "100GB-1TB", "1-10TB", "10-100TB", "> 100TB"]
        )
    
    with col2:
        st.subheader("Non-Functional Requirements")
        availability_target = st.selectbox(
            "Availability Target", 
            ["99.5% (43.8h downtime/year)", "99.9% (8.77h downtime/year)", "99.95% (4.38h downtime/year)", "99.99% (52.6min downtime/year)"]
        )
        
        compliance = st.multiselect(
            "Compliance Requirements",
            ["PCI DSS", "HIPAA", "SOC 2", "GDPR", "ISO 27001", "FedRAMP", "None"]
        )
        
        performance_req = st.selectbox(
            "Performance Requirements",
            ["Standard (< 1s response)", "Fast (< 500ms response)", "Real-time (< 100ms response)"]
        )
    
    with col3:
        st.subheader("Deployment & Growth")
        deployment_regions = st.multiselect(
            "Deployment Regions",
            ["us-east-1", "us-west-2", "eu-west-1", "ap-south-1", "ap-southeast-1"],
            default=["ap-south-1"]
        )
        
        growth_projection = st.selectbox(
            "12-Month Growth Projection",
            ["2x users", "5x users", "10x users", "100x users"]
        )
        
        budget_range = st.selectbox(
            "Monthly Budget Range",
            ["$500-2K", "$2K-10K", "$10K-50K", "$50K+"]
        )
    
    if st.button("🚀 Design Complete Solution", type="primary", key="design_solution"):
        with st.spinner("Designing architecture with AWS best practices..."):
            try:
                request_data = {
                    "app_type": app_type,
                    "expected_users": expected_users,
                    "data_volume": data_volume,
                    "availability_target": availability_target,
                    "compliance": compliance,
                    "performance_requirements": performance_req,
                    "regions": deployment_regions,
                    "growth_projection": growth_projection,
                    "budget_range": budget_range
                }
                
                result = api_client.design_complete_solution(request_data)
                
                st.success("✅ Solution Design Complete!")
                
                # Display results
                with st.expander("🏗️ Complete Architecture Design", expanded=True):
                    st.markdown(result["response"])
                
                # Show tools used
                if result.get("tools_used"):
                    st.info(f"Design used {len(result['tools_used'])} specialized AWS tools")
                    
            except Exception as e:
                st.error(f"Design failed: {e}")

# Tab 3: General Chat
with tab3:
    st.subheader("💬 General AWS Consultation")
    st.markdown("Ask any AWS-related questions. The assistant will automatically use the appropriate specialized tools.")
    
    # Display conversation history
    for i, (role, content, timestamp) in enumerate(st.session_state.conversation_history):
        if role == "user":
            with st.chat_message("user"):
                st.write(content)
        else:
            with st.chat_message("assistant"):
                st.write(content)
    
    # Chat input
    if prompt := st.chat_input("Ask about AWS costs, architecture, or best practices..."):
        # Add user message to history
        st.session_state.conversation_history.append(("user", prompt, datetime.now().isoformat()))
        
        with st.chat_message("user"):
            st.write(prompt)
        
        with st.chat_message("assistant"):
            with st.spinner("Analyzing and gathering AWS data..."):
                try:
                    response = api_client.chat_with_context(prompt, st.session_state.session_id)
                    
                    assistant_response = response["response"]
                    st.write(assistant_response)
                    
                    # Show query type if detected
                    if response.get("query_type") and response["query_type"] != "general":
                        st.info(f"🎯 Detected query type: {response['query_type'].replace('_', ' ').title()}")
                    
                    # Show tools used
                    if response.get("tools_used"):
                        with st.expander(f"🔧 Used {len(response['tools_used'])} AWS tools"):
                            for tool in response["tools_used"]:
                                status = "✅" if tool.get("success") else "❌"
                                st.write(f"{status} {tool.get('tool_name', 'Unknown')} ({tool.get('server_name', 'Unknown server')})")
                    
                    # Add assistant response to history
                    st.session_state.conversation_history.append(("assistant", assistant_response, datetime.now().isoformat()))
                    
                except Exception as e:
                    error_msg = f"Error: {str(e)}"
                    st.error(error_msg)
                    st.session_state.conversation_history.append(("assistant", error_msg, datetime.now().isoformat()))

# Tab 4: Analytics
with tab4:
    st.subheader("📊 Usage Analytics & Tool Performance")
    
    try:
        tools_data = api_client.get_tools_by_category()
        
        # Create tool category distribution
        categories = {}
        for tool_info in tools_data.values():
            category = tool_info.get('category', 'general')
            categories[category] = categories.get(category, 0) + 1
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Tools by Category")
            if categories:
                fig = px.pie(
                    values=list(categories.values()), 
                    names=list(categories.keys()),
                    title="Available AWS Tools by Category"
                )
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("Server Status Overview")
            servers_status = api_client.get_servers_status()
            
            connected = sum(1 for info in servers_status.values() if info["status"] == "connected")
            total = len(servers_status)
            
            fig = go.Figure(data=[
                go.Bar(name='Connected', x=['Servers'], y=[connected], marker_color='green'),
                go.Bar(name='Disconnected', x=['Servers'], y=[total - connected], marker_color='red')
            ])
            fig.update_layout(barmode='stack', title='Server Connection Status')
            st.plotly_chart(fig, use_container_width=True)
        
        # Tool details table
        st.subheader("Available Tools Details")
        
        tool_details = []
        for tool_key, tool_info in tools_data.items():
            tool_details.append({
                "Tool Name": tool_info["name"],
                "Server": tool_info["server"], 
                "Category": tool_info["category"].replace('_', ' ').title(),
                "Description": tool_info["description"][:100] + "..." if len(tool_info["description"]) > 100 else tool_info["description"]
            })
        
        if tool_details:
            df = pd.DataFrame(tool_details)
            st.dataframe(df, use_container_width=True)
    
    except Exception as e:
        st.error(f"Failed to load analytics: {e}")

# Footer
st.markdown("---")
st.markdown("**Session ID:** `" + st.session_state.session_id + "`")

if st.button("🗑️ Clear Conversation", key="clear_conv"):
    st.session_state.conversation_history = []
    st.rerun()
