"""
Enhanced FastAPI Backend for MCP Client with Specialized AWS Capabilities
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import logging
from contextlib import asynccontextmanager
import uvicorn
import os
import uuid

from mcp_backend import (
    MCPServerConfig, ChatRequest,
    MCPServerConnection, MCPClientManager, DEFAULT_MCP_SERVERS,
    get_executable_name
)

# Import Bedrock session management
from session_manager_new import session_manager
from enhanced_mcp_manager import EnhancedMCPMixin

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMCPClientManager(MCPClientManager, EnhancedMCPMixin):
    """Enhanced MCP Client Manager with specialized AWS capabilities."""
    
    def __init__(self):
        super().__init__()
        self.model_id = os.getenv("BEDROCK_MODEL_ID", "apac.amazon.nova-lite-v1:0")
        self._async_bedrock_client = None
        logger.info("Enhanced MCP Client Manager initialized with specialized AWS capabilities")

    async def get_async_bedrock_runtime(self):
        """Provide a pooled async bedrock-runtime client."""
        try:
            import aioboto3
        except ImportError as e:
            raise ImportError("aioboto3 is required for async Bedrock operations. pip install aioboto3") from e

        if self._async_bedrock_client is None:
            session = aioboto3.Session()
            self._async_bedrock_client = await session.client("bedrock-runtime").__aenter__()
        
        return self._async_bedrock_client

class EnhancedChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"
    session_stats: Optional[Dict[str, Any]] = None
    context_used: bool = False
    query_type: Optional[str] = None  # New field to indicate query type

enhanced_mcp_manager = EnhancedMCPClientManager()

# [Previous background workers remain the same]
async def session_monitor_worker():
    """Background worker to monitor Bedrock session health."""
    try:
        while True:
            try:
                stats = session_manager.get_all_sessions_stats()
                active_count = stats.get("active_sessions", 0)
                if active_count > 0:
                    logger.info(f"Active Bedrock sessions: {active_count}")
                await asyncio.sleep(300)
            except asyncio.CancelledError:
                logger.info("Session monitor worker cancelled")
                break
            except Exception as e:
                logger.error(f"Session monitor error: {e}")
                await asyncio.sleep(60)
    except asyncio.CancelledError:
        logger.info("Session monitor worker cancelled")
    except Exception as e:
        logger.error(f"Session monitor worker error: {e}")

async def configure_servers_background():
    """Background task to configure MCP servers after startup."""
    logger.info("Starting background MCP server configuration...")
    
    async def configure_server_safe(server_config: MCPServerConfig):
        if not server_config.enabled:
            return False, f"Server {server_config.name} is disabled"
        
        logger.info(f"Adding server: {server_config.name}")
        max_retries = 3 if server_config.name in ["cost-explorer", "aws-pricing", "billing-cost-management"] else 2
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    logger.info(f"Retry attempt {attempt + 1} for {server_config.name}")
                    await asyncio.sleep(2.0)
                
                success = await enhanced_mcp_manager.add_server(server_config)
                if success:
                    logger.info(f"✅ Successfully configured {server_config.name}")
                    return True, None
                else:
                    connection = enhanced_mcp_manager.connections.get(server_config.name)
                    error_msg = connection.error if connection else "Unknown error"
                    
                    if "timeout" not in error_msg.lower() or attempt == max_retries - 1:
                        logger.error(f"❌ Failed to configure {server_config.name}: {error_msg}")
                        return False, error_msg
                    else:
                        logger.warning(f"⚠️ Timeout for {server_config.name}, will retry...")
                        
            except asyncio.CancelledError:
                logger.info(f"Configuration cancelled for {server_config.name}")
                return False, "Cancelled"
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"❌ Error configuring {server_config.name}: {e}")
                    return False, str(e)
                else:
                    logger.warning(f"⚠️ Exception for {server_config.name}, will retry: {e}")
        
        return False, "Max retries exceeded"

    try:
        results = []
        for config in DEFAULT_MCP_SERVERS:
            logger.info(f"Configuring server: {config.name}")
            result = await configure_server_safe(config)
            results.append(result)
            await asyncio.sleep(1.0)  # Delay between server configurations

        successful_count = sum(1 for res in results if isinstance(res, tuple) and res[0])
        logger.info(f"🚀 Background server configuration complete: {successful_count}/{len(DEFAULT_MCP_SERVERS)} servers connected")
        
        if successful_count == 0:
            logger.warning("⚠️ No MCP servers connected")
    except asyncio.CancelledError:
        logger.info("Background server configuration cancelled")
    except Exception as e:
        logger.error(f"Error in background server configuration: {e}")

@asynccontextmanager
async def enhanced_lifespan(app: FastAPI):
    """Enhanced lifespan with specialized AWS capabilities validation."""
    logger.info("Starting Enhanced AWS Cost Optimization & Architecture Design API")

    # Validate Bedrock availability
    try:
        test_stats = session_manager.get_all_sessions_stats()
        if not session_manager.backend:
            raise RuntimeError("Bedrock backend required but not available")
        logger.info("✅ Bedrock session management validated successfully")
    except Exception as e:
        logger.error(f"❌ STARTUP FAILED: Bedrock validation failed: {e}")
        raise

    monitor_task = asyncio.create_task(session_monitor_worker())
    
    # Start server configuration in background
    config_task = None
    if os.getenv("AUTO_CONFIGURE_SERVERS", "true").lower() == "true":
        logger.info("Starting background MCP server configuration...")
        config_task = asyncio.create_task(configure_servers_background())

    try:
        yield
    finally:
        logger.info("Shutting down Enhanced AWS API")
        
        # Cancel background tasks
        if config_task and not config_task.done():
            config_task.cancel()
            try:
                await config_task
            except asyncio.CancelledError:
                pass

        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass

        try:
            await enhanced_mcp_manager.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

app = FastAPI(
    title="Enhanced AWS Cost Optimization & Architecture Design API",
    description="Specialized MCP client for AWS cost analysis and solution design",
    version="2.1.0",
    lifespan=enhanced_lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Health check endpoint with enhanced capabilities."""
    session_stats = session_manager.get_all_sessions_stats()
    
    # Count connected servers by capability
    connected_servers = {}
    for name, connection in enhanced_mcp_manager.connections.items():
        if connection.status == "connected":
            connected_servers[name] = len(connection.tools)
    
    return {
        "message": "Enhanced AWS Cost Optimization & Architecture Design API is running",
        "status": "healthy",
        "mode": "specialized_aws",
        "features": [
            "service_addition_cost_analysis",
            "complete_solution_design", 
            "context_retention",
            "bedrock_persistence",
            "specialized_routing"
        ],
        "connected_servers": connected_servers,
        "session_stats": session_stats
    }

@app.post("/chat", response_model=EnhancedChatResponse)
async def enhanced_chat_endpoint(request: ChatRequest):
    """Enhanced chat endpoint with specialized routing."""
    try:
        conversation_id = request.conversation_id or f"conv_{uuid.uuid4().hex[:8]}"
        
        # Detect query type for specialized routing
        message_lower = request.message.lower()
        query_type = "general"
        
        if any(keyword in message_lower for keyword in ["add service", "adding", "cost impact", "incremental cost", "current", "existing"]):
            query_type = "service_addition_cost_analysis"
            # Prioritize cost analysis tools
            specialized_tools = ["cost-explorer", "aws-pricing", "billing-cost-management", "cloud-control-api"]
        elif any(keyword in message_lower for keyword in ["design", "architecture", "from scratch", "deploy", "solution", "complete", "build"]):
            query_type = "complete_solution_design"
            # Prioritize architecture design tools
            specialized_tools = ["aws-cdk", "aws-diagram", "aws-pricing", "aws-documentation", "aws-cloudwatch", "cloudformation"]
        else:
            specialized_tools = []
        
        # Get all available tools plus prioritized ones for the query type
        available_tools = list(enhanced_mcp_manager.get_available_tools().keys()) if request.use_tools else []
        
        # Reorder tools to prioritize specialized ones for the detected query type
        if specialized_tools and available_tools:
            prioritized_tools = []
            for specialized in specialized_tools:
                for tool in available_tools:
                    if tool.startswith(f"{specialized}::"):
                        prioritized_tools.append(tool)
            
            # Add remaining tools
            remaining_tools = [tool for tool in available_tools if tool not in prioritized_tools]
            available_tools = prioritized_tools + remaining_tools

        logger.info(f"Detected query type: {query_type}, using {len(available_tools)} tools")

        result = await enhanced_mcp_manager.chat_with_bedrock_with_context(
            message=request.message,
            session_id=conversation_id,
            tools_available=available_tools
        )

        chat_session = session_manager.get_session(conversation_id)
        session_stats = chat_session.get_session_stats() if chat_session else {"error": "Session not found"}

        return EnhancedChatResponse(
            response=result["response"],
            conversation_id=conversation_id,
            tools_used=result["tools_used"],
            status="success" if not result.get("error") else "error",
            session_stats=session_stats,
            context_used=session_stats.get('total_turns', 0) > 0 if session_stats and 'error' not in session_stats else False,
            query_type=query_type
        )

    except Exception as e:
        logger.error(f"Enhanced chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Specialized endpoints
@app.post("/analyze/service-addition")
async def analyze_service_addition(request: dict):
    """Specialized endpoint for service addition cost analysis."""
    try:
        prompt = f"""
        Analyze the cost impact of adding {request.get('new_service', 'unspecified service')} to my current AWS setup.
        
        Current services: {', '.join(request.get('current_services', []))}
        Expected usage: {request.get('usage_pattern', 'not specified')}
        Region: {request.get('region', 'ap-south-1')}
        
        Please provide:
        1. Incremental monthly/annual costs
        2. Optimization opportunities
        3. Alternative service options
        4. Cost comparison analysis
        """
        
        chat_request = ChatRequest(message=prompt, use_tools=True)
        return await enhanced_chat_endpoint(chat_request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/design/complete-solution")
async def design_complete_solution(request: dict):
    """Specialized endpoint for complete solution architecture design."""
    try:
        prompt = f"""
        Design a complete AWS solution architecture for:
        
        Application type: {request.get('app_type', 'web application')}
        Expected users: {request.get('expected_users', 10000)}
        Availability target: {request.get('availability_target', '99.9%')}
        Compliance requirements: {', '.join(request.get('compliance', []))}
        Regions: {', '.join(request.get('regions', ['ap-south-1']))}
        Growth projection: {request.get('growth_projection', '2x in 12 months')}
        
        Please provide:
        1. Complete architecture diagrams
        2. Detailed cost breakdown (setup + operational)
        3. System design requirements document
        4. Implementation roadmap
        5. Monitoring and alerting strategy
        """
        
        chat_request = ChatRequest(message=prompt, use_tools=True)
        return await enhanced_chat_endpoint(chat_request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# [Include all other endpoints from the previous main_enhanced.py - sessions, servers, tools, etc.]

@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session."""
    chat_session = session_manager.get_session(session_id)
    if not chat_session:
        raise HTTPException(status_code=404, detail="Session not found")

    session_stats = chat_session.get_session_stats()
    history = [turn.to_dict() for turn in chat_session.conversation_history]

    return {
        "session_id": session_id,
        "created_at": session_stats.get('created_at'),
        "last_activity": session_stats.get('last_activity'),
        "message_count": len(history),
        "total_tools_used": session_stats.get('total_tools_used', 0),
        "history": history
    }

@app.get("/sessions/{session_id}/stats")
async def get_session_stats(session_id: str):
    """Get detailed statistics for a session."""
    chat_session = session_manager.get_session(session_id)
    if not chat_session:
        raise HTTPException(status_code=404, detail="Session not found")
    return chat_session.get_session_stats()

@app.delete("/sessions/{session_id}")
async def clear_session(session_id: str):
    """Delete a session."""
    try:
        session_manager.delete_session(session_id)
        return {"message": f"Session {session_id} deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=404, detail="Session not found or could not be deleted")

@app.get("/sessions")
async def list_sessions():
    """List all active sessions with basic stats."""
    sessions_info = {}
    for sid, chat_session in session_manager.sessions.items():
        try:
            session_stats = chat_session.get_session_stats()
            sessions_info[sid] = session_stats
        except Exception as e:
            logger.warning(f"Could not get stats for session {sid}: {e}")

    return {
        "sessions": sessions_info,
        "total_sessions": len(sessions_info),
        "global_stats": session_manager.get_all_sessions_stats()
    }

@app.get("/servers")
async def list_servers():
    """List all configured MCP servers with specialization info."""
    servers_info = {}
    for name, connection in enhanced_mcp_manager.connections.items():
        # Categorize servers by capability
        if name in ["cost-explorer", "aws-pricing", "billing-cost-management"]:
            category = "cost_analysis"
        elif name in ["aws-cdk", "aws-diagram", "aws-documentation", "cloudformation"]:
            category = "architecture_design"
        elif name in ["cloud-control-api", "aws-cloudwatch"]:
            category = "resource_management"
        else:
            category = "general"
        
        servers_info[name] = {
            "name": name,
            "status": connection.status,
            "tools_count": len(connection.tools),
            "resources_count": len(connection.resources),
            "description": connection.config.description,
            "enabled": connection.config.enabled,
            "error": connection.error,
            "category": category
        }
    
    return servers_info

@app.get("/tools")
async def list_tools():
    """List all available tools categorized by capability."""
    tools = {}
    available_tools = enhanced_mcp_manager.get_available_tools()
    
    for tool_key, tool_data in available_tools.items():
        server_name = tool_data["server"]
        
        # Categorize by server type
        if server_name in ["cost-explorer", "aws-pricing", "billing-cost-management"]:
            category = "cost_analysis"
        elif server_name in ["aws-cdk", "aws-diagram", "aws-documentation", "cloudformation"]:
            category = "architecture_design"
        elif server_name in ["cloud-control-api", "aws-cloudwatch"]:
            category = "resource_management"
        else:
            category = "general"
        
        tools[tool_key] = {
            "server": tool_data["server"],
            "name": tool_data["tool"]["name"],
            "description": tool_data["tool"]["description"],
            "input_schema": tool_data["tool"].get("input_schema", {}),
            "category": category
        }

    return tools

if __name__ == "__main__":
    uvicorn.run(
        "main_enhanced:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info")
    )
